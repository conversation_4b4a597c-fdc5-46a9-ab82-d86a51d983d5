#!/usr/bin/env python3
"""
测试verify动作功能
验证新增的verify动作是否能正确解析和执行
"""

import json
from unittest.mock import Mock, patch, MagicMock

from src.domain.ui_task.mobile.android.action_tool import ActionParser, ActionExecutor, ActionValidator
from src.schema.action_types import ActionType


def test_verify_action_parsing():
    """测试verify动作的解析功能"""
    print("🧪 测试verify动作解析...")
    
    # 创建解析器
    parser = ActionParser("test_device", "test_task")
    
    # 测试各种verify动作格式
    test_cases = [
        "verify(content='首页被激活')",
        "verify(content=\"王者标签\")",
        "verify(content='页面左上角首页被激活')",
        "verify(content='二级导航栏中看到王者标签')"
    ]
    
    for action_str in test_cases:
        result = parser.parse(action_str)
        assert result is not None, f"解析失败: {action_str}"
        assert result["action"] == ActionType.VERIFY.value, f"动作类型错误: {result}"
        assert "content" in result, f"缺少content参数: {result}"
        print(f"✅ 解析成功: {action_str} -> {result}")
    
    print("✅ verify动作解析测试通过")


def test_verify_action_validation():
    """测试verify动作的验证功能"""
    print("🧪 测试verify动作验证...")
    
    validator = ActionValidator()
    
    # 测试有效的verify动作
    valid_action = {
        "action": ActionType.VERIFY.value,
        "content": "首页被激活"
    }
    assert validator.validate(valid_action), "有效动作验证失败"
    print("✅ 有效动作验证通过")
    
    # 测试无效的verify动作（缺少content）
    invalid_action = {
        "action": ActionType.VERIFY.value
    }
    assert not validator.validate(invalid_action), "无效动作应该验证失败"
    print("✅ 无效动作验证通过")
    
    print("✅ verify动作验证测试通过")


def test_verify_action_execution():
    """测试verify动作的执行功能"""
    print("🧪 测试verify动作执行...")
    
    # 模拟依赖
    mock_screenshot_manager = Mock()
    mock_screenshot_manager.take_screenshot.return_value = "test_screenshot.png"
    
    mock_convert_screenshot = Mock()
    mock_convert_screenshot.return_value = "base64_screenshot_data"
    
    mock_model = Mock()
    mock_response = Mock()
    mock_response.content = json.dumps({
        "verified": True,
        "analysis": "在截图中找到了'首页被激活'相关的UI元素",
        "found_elements": ["首页", "激活状态"]
    })
    mock_model.invoke.return_value = mock_response
    
    with patch('src.domain.ui_task.mobile.android.action_tool.screenshot_manager', mock_screenshot_manager), \
         patch('src.domain.ui_task.mobile.android.action_tool.convert_screenshot_to_base64', mock_convert_screenshot), \
         patch('src.infra.model.get_chat_model', return_value=mock_model):
        
        # 创建执行器
        executor = ActionExecutor("test_device", "test_task")
        
        # 测试verify动作执行
        result = executor.execute("verify(content='首页被激活')")
        
        # 验证结果
        assert result.status == "success", f"执行状态错误: {result.status}"
        assert result.action == ActionType.VERIFY.value, f"动作类型错误: {result.action}"
        assert "verify" in result.data, f"缺少验证数据: {result.data}"
        assert result.data["verify"]["verified"] == True, f"验证结果错误: {result.data}"
        assert result.data["verify"]["content"] == "首页被激活", f"验证内容错误: {result.data}"
        
        print(f"✅ 执行成功: {result.message}")
        print(f"✅ 验证数据: {result.data['verify']}")
    
    print("✅ verify动作执行测试通过")


def test_verify_action_failure():
    """测试verify动作执行失败的情况"""
    print("🧪 测试verify动作执行失败...")
    
    # 模拟依赖
    mock_screenshot_manager = Mock()
    mock_screenshot_manager.take_screenshot.return_value = "test_screenshot.png"
    
    mock_convert_screenshot = Mock()
    mock_convert_screenshot.return_value = "base64_screenshot_data"
    
    mock_model = Mock()
    mock_response = Mock()
    mock_response.content = json.dumps({
        "verified": False,
        "analysis": "在截图中未找到'王者标签'相关的UI元素",
        "found_elements": []
    })
    mock_model.invoke.return_value = mock_response
    
    with patch('src.domain.ui_task.mobile.android.action_tool.screenshot_manager', mock_screenshot_manager), \
         patch('src.domain.ui_task.mobile.android.action_tool.convert_screenshot_to_base64', mock_convert_screenshot), \
         patch('src.infra.model.get_chat_model', return_value=mock_model):
        
        # 创建执行器
        executor = ActionExecutor("test_device", "test_task")
        
        # 测试verify动作执行失败
        result = executor.execute("verify(content='王者标签')")
        
        # 验证结果
        assert result.status == "failed", f"执行状态应该是failed: {result.status}"
        assert result.action == ActionType.VERIFY.value, f"动作类型错误: {result.action}"
        assert "verify" in result.data, f"缺少验证数据: {result.data}"
        assert result.data["verify"]["verified"] == False, f"验证结果应该是False: {result.data}"
        
        print(f"✅ 执行失败（预期）: {result.message}")
        print(f"✅ 验证数据: {result.data['verify']}")
    
    print("✅ verify动作执行失败测试通过")


def test_verify_action_integration():
    """测试verify动作的集成功能"""
    print("🧪 测试verify动作集成...")
    
    # 测试在决策Agent中使用verify动作的场景
    test_step = {
        "step": "1.判断当前是否出于首页，页面左上角'首页被激活'",
        "expect_result": {
            "wait_time": 1.5
        }
    }
    
    # 模拟决策Agent输出verify动作
    decision_output = {
        "interface_analysis": "当前界面显示了主页内容",
        "current_step_name": "1.判断当前是否出于首页，页面左上角'首页被激活'",
        "action_decision": "需要验证页面左上角是否显示'首页被激活'状态",
        "instruction": "验证首页激活状态",
        "action": "verify(content='首页被激活')"
    }
    
    print(f"✅ 决策输出: {decision_output['action']}")
    
    # 验证动作格式正确
    parser = ActionParser("test_device", "test_task")
    parsed_action = parser.parse(decision_output["action"])
    
    assert parsed_action is not None, "动作解析失败"
    assert parsed_action["action"] == ActionType.VERIFY.value, "动作类型错误"
    assert parsed_action["content"] == "首页被激活", "验证内容错误"
    
    print("✅ verify动作集成测试通过")


if __name__ == "__main__":
    print("🚀 开始测试verify动作功能...")
    
    try:
        test_verify_action_parsing()
        test_verify_action_validation()
        test_verify_action_execution()
        test_verify_action_failure()
        test_verify_action_integration()
        
        print("\n🎉 所有测试通过！verify动作功能添加成功")
        print("\n📋 使用说明:")
        print("1. 在测试用例中可以使用 verify(content='要验证的内容') 动作")
        print("2. 该动作会使用AI模型分析当前截图，判断是否包含指定内容")
        print("3. 验证成功返回success状态，失败返回failed状态")
        print("4. 适用于步骤验证，如判断页面状态、检查UI元素等")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        raise
